const ACCESS_TOKEN: &str = "a6af29a286fe2625";
const BASE_URL: &str = "https://hackattic.com/challenges";

pub struct HackatticClient {
    challenge_name: String,
}

impl HackatticClient {
    pub fn new(challenge_name: &str) -> Self {
        Self {
            challenge_name: challenge_name.to_string(),
        }
    }

    /// Get the problem data from the Hackattic API
    pub fn get_problem(&self) -> serde_json::Value {
        let url = format!(
            "{}/{}/problem?access_token={}",
            BASE_URL, self.challenge_name, ACCESS_TOKEN
        );

        reqwest::blocking::get(&url)
            .expect("Failed to fetch problem")
            .json::<serde_json::Value>()
            .expect("Failed to parse JSON")
    }

    /// Submit a solution to the Hackattic API
    pub fn submit_solution(&self, solution: serde_json::Value) {
        let url = format!(
            "{}/{}/solve?access_token={}",
            BASE_URL, self.challenge_name, ACCESS_TOKEN
        );

        let resp = reqwest::blocking::Client::new()
            .post(&url)
            .json(&solution)
            .send()
            .expect("Failed to send POST");

        let status = resp.status();
        let text = resp.text().expect("Failed to read response body");
        println!("Status: {}", status);
        println!("Response: {}", text);
    }

    /// Download a file from a URL
    pub fn download_file(&self, url: &str) -> Vec<u8> {
        reqwest::blocking::get(url)
            .expect("Failed to download file")
            .bytes()
            .expect("Failed to read file bytes")
            .to_vec()
    }
}
